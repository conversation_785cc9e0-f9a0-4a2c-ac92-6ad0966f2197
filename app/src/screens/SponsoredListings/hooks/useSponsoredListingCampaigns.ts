import { useCallback, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { campaignsApi } from 'src/data/api/campaigns';
import {
  BidStrategyTypeDto,
  CampaignDtoType,
  CampaignTypeEnum,
} from 'src/data/schemas/api/campaigns';
import { useUserContext } from 'src/hooks/useUser';
import { adManager } from 'src/data/api/trade-app-bff/ad-manager';

const SPONSORED_LISTING_CAMPAIGNS_QUERY_KEY = 'sponsored-listing-campaigns';
const SPONSORED_LISTING_BID_STRATEGIES_QUERY_KEY =
  'sponsored-listing-bid-strategies';

export type UseSponsoredListingCampaignsReturn = {
  sponsoredCampaigns: CampaignDtoType[];
  campaigns: CampaignDtoType[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
};

/**
 * Hook to fetch campaigns that can be boosted for sponsored listings
 * Filters campaigns to only include Ppl or Fixed types
 */
export function useSponsoredListingCampaigns(): UseSponsoredListingCampaignsReturn {
  const { companyId } = useUserContext();

  const fetchCampaigns = useCallback(async () => {
    if (!companyId) {
      throw new Error('Company ID is required to fetch campaigns');
    }

    const response = await campaignsApi.getCampaigns(companyId, {
      includeInactive: true,
      pageSize: 100,
    });

    return response.data.items;
  }, [companyId]);

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: [SPONSORED_LISTING_CAMPAIGNS_QUERY_KEY, companyId],
    queryFn: fetchCampaigns,
    enabled: Boolean(companyId),
  });

  const filteredCampaigns = useMemo(() => {
    if (!data) {
      return [];
    }

    return data.filter(
      (campaign) =>
        campaign.campaignType === CampaignTypeEnum.Ppl ||
        campaign.campaignType === CampaignTypeEnum.Fixed,
    );
  }, [data]);

  // Get sponsored campaigns without bid strategies first
  const baseSponsoredCampaigns = useMemo(() => {
    if (!data) {
      return [];
    }

    return data.filter(
      (campaign) =>
        campaign.campaignType === CampaignTypeEnum.MdpSponsoredSearch,
    );
  }, [data]);

  // Fetch bid strategies for sponsored campaigns
  const fetchBidStrategies = useCallback(async () => {
    if (!companyId || baseSponsoredCampaigns.length === 0) {
      return {};
    }

    const bidStrategyPromises = baseSponsoredCampaigns.map(async (campaign) => {
      try {
        const bidStrategy = await adManager.getSponsoredListingBidStrategy(
          campaign.campaignId,
          companyId,
        );
        // Transform lowercase server response to title case DTO format
        const serverBidStrategy = bidStrategy.data?.bidStrategy;
        let transformedBidStrategy = BidStrategyTypeDto.None;

        if (serverBidStrategy) {
          switch (serverBidStrategy.toLowerCase()) {
            case 'player':
              transformedBidStrategy = BidStrategyTypeDto.Player;
              break;
            case 'leader':
              transformedBidStrategy = BidStrategyTypeDto.Leader;
              break;
            case 'competitor':
              transformedBidStrategy = BidStrategyTypeDto.Competitor;
              break;
            case 'none':
            default:
              transformedBidStrategy = BidStrategyTypeDto.None;
              break;
          }
        }

        return {
          campaignId: campaign.campaignId,
          bidStrategy: transformedBidStrategy,
        };
      } catch (err) {
        console.warn(
          `Failed to fetch bid strategy for campaign ${campaign.campaignId}:`,
          err,
        );
        return {
          campaignId: campaign.campaignId,
          bidStrategy: BidStrategyTypeDto.None,
        };
      }
    });

    const bidStrategies = await Promise.all(bidStrategyPromises);
    return bidStrategies.reduce(
      (acc, { campaignId, bidStrategy }) => {
        acc[campaignId] = bidStrategy;
        return acc;
      },
      {} as Record<string, BidStrategyTypeDto>,
    );
  }, [companyId, baseSponsoredCampaigns]);

  const { data: bidStrategiesData } = useQuery({
    queryKey: [
      SPONSORED_LISTING_BID_STRATEGIES_QUERY_KEY,
      companyId,
      baseSponsoredCampaigns.map((c) => c.campaignId).join(','),
    ],
    queryFn: fetchBidStrategies,
    enabled: Boolean(companyId) && baseSponsoredCampaigns.length > 0,
    staleTime: 5 * 60 * 1000,
  });

  // Combine sponsored campaigns with their bid strategies
  const sponsoredCampaigns = useMemo(() => {
    if (!baseSponsoredCampaigns.length) {
      return [];
    }

    return baseSponsoredCampaigns.map((campaign) => {
      const bidStrategy =
        bidStrategiesData?.[campaign.campaignId] || BidStrategyTypeDto.None;

      return {
        ...campaign,
        mdpSponsoredSearch: campaign.mdpSponsoredSearch
          ? {
              ...campaign.mdpSponsoredSearch,
              bidStrategy,
            }
          : undefined,
      } as CampaignDtoType;
    });
  }, [baseSponsoredCampaigns, bidStrategiesData]);

  return {
    sponsoredCampaigns,
    campaigns: filteredCampaigns,
    isLoading,
    error: error as Error | null,
    refetch,
  };
}
